#!/usr/bin/env python3
"""
Real-time Speech-to-Text CLI Application

This application automatically detects the best audio input device,
captures audio in real-time, and displays transcribed speech as you talk.

Usage:
    python realtime_speech_to_text.py [options]

Example:
    python realtime_speech_to_text.py --model base --language en
"""

import os
import sys
import argparse
import threading
import time
import queue
import numpy as np
import pyaudio
import whisper
from typing import Optional, List, Dict, Any, Tuple, NamedTuple
import warnings
from pathlib import Path
from datetime import datetime
import collections
import uuid
import json
from dataclasses import dataclass, field

# Suppress some warnings for cleaner output
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)


@dataclass
class AudioSegment:
    """Represents an audio segment with metadata for processing."""
    id: str = field(default_factory=lambda: str(uuid.uuid4())[:8])
    audio_data: np.ndarray = field(default_factory=lambda: np.array([]))
    timestamp: datetime = field(default_factory=datetime.now)
    duration: float = 0.0
    is_complete: bool = False
    is_word_boundary: bool = False
    energy_level: float = 0.0

    def __post_init__(self):
        """Calculate duration and energy after initialization."""
        if len(self.audio_data) > 0:
            self.duration = len(self.audio_data) / 16000  # Assuming 16kHz sample rate
            self.energy_level = np.sqrt(np.mean(self.audio_data ** 2))


@dataclass
class TranscriptionResult:
    """Represents a transcription result with metadata."""
    id: str
    text: str
    timestamp: datetime
    duration: float
    confidence: float = 0.0
    processing_time: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'text': self.text,
            'timestamp': self.timestamp.isoformat(),
            'duration': self.duration,
            'confidence': self.confidence,
            'processing_time': self.processing_time
        }


def find_local_model(model_name: str) -> Optional[str]:
    """
    Find a local model file in common locations.

    Args:
        model_name: Name of the model to find

    Returns:
        Path to the model file if found, None otherwise
    """
    # Possible model filenames
    model_filenames = [
        f"{model_name}.pt",
        f"{model_name}-v3.pt" if model_name == "large" else f"{model_name}.pt"
    ]

    # Search locations (in order of preference)
    search_paths = [
        # Local models directory (for bundled distributions)
        Path("./models"),
        Path("./whisper_models"),

        # Relative to executable (for PyInstaller)
        Path(sys.executable).parent / "models",
        Path(sys.executable).parent / "whisper_models",

        # Standard cache locations
        Path.home() / ".cache" / "whisper",
        Path(os.environ.get("APPDATA", "")) / "whisper" if os.name == "nt" else Path.home() / ".cache" / "whisper",
    ]

    for search_path in search_paths:
        if search_path.exists():
            for filename in model_filenames:
                model_path = search_path / filename
                if model_path.exists():
                    print(f"📁 Found local model: {model_path}")
                    return str(model_path)

    return None


def load_whisper_model(model_name: str):
    """
    Load a Whisper model, preferring local files over downloads.

    Args:
        model_name: Name of the model to load

    Returns:
        Loaded Whisper model
    """
    # Try to find local model first
    local_model_path = find_local_model(model_name)

    if local_model_path:
        try:
            print(f"🔄 Loading local model from: {local_model_path}")
            return whisper.load_model(local_model_path)
        except Exception as e:
            print(f"⚠️  Failed to load local model: {e}")
            print("🔄 Falling back to download...")

    # Fall back to standard download
    print(f"🔄 Downloading model '{model_name}'...")
    return whisper.load_model(model_name)


class VoiceActivityDetector:
    """Enhanced Voice Activity Detection with word boundary detection."""

    def __init__(self, sample_rate: int = 16000, frame_duration_ms: int = 30):
        """
        Initialize Voice Activity Detector.

        Args:
            sample_rate: Audio sample rate in Hz
            frame_duration_ms: Frame duration in milliseconds
        """
        self.sample_rate = sample_rate
        self.frame_duration_ms = frame_duration_ms
        self.frame_size = int(sample_rate * frame_duration_ms / 1000)

        # VAD parameters (optimized for practical use)
        self.energy_threshold = 0.002  # Base threshold
        self.silence_duration_ms = 800  # Longer silence for complete speech segments
        self.speech_pad_ms = 300  # Adequate padding for better capture
        self.min_speech_frames = 15  # Higher minimum for better quality
        self.speech_multiplier = 2.5  # Speech energy multiplier

        # Word boundary detection parameters (more conservative)
        self.word_boundary_silence_ms = 500  # Longer silence for word boundaries
        self.max_segment_duration_ms = 1000  # Maximum segment duration (1 second)
        self.word_boundary_frames = int(self.word_boundary_silence_ms / frame_duration_ms)
        self.max_segment_frames = int(self.max_segment_duration_ms / frame_duration_ms)

        # State tracking
        self.silence_frames = int(self.silence_duration_ms / frame_duration_ms)
        self.pad_frames = int(self.speech_pad_ms / frame_duration_ms)
        self.consecutive_silence = 0
        self.is_speaking = False
        self.speech_start_time = None

        # Enhanced buffer management
        self.pre_speech_buffer = collections.deque(maxlen=self.pad_frames)
        self.current_segment = AudioSegment()
        self.segment_frames = []
        self.segment_start_time = None

        # Energy history for adaptive threshold
        self.energy_history = collections.deque(maxlen=100)
        self.adaptive_threshold = True

        # Debug and statistics
        self.frame_count = 0
        self.debug_interval = 100
        self.debug_enabled = False
        self.segments_created = 0

    def calculate_energy(self, audio_frame: np.ndarray) -> float:
        """Calculate RMS energy of audio frame."""
        if len(audio_frame) == 0:
            return 0.0
        return np.sqrt(np.mean(audio_frame ** 2))

    def update_adaptive_threshold(self, energy: float):
        """Update adaptive energy threshold based on background noise."""
        self.energy_history.append(energy)
        if len(self.energy_history) >= 50:  # Need enough samples
            # Set threshold based on background noise level
            mean_energy = np.mean(self.energy_history)
            std_energy = np.std(self.energy_history)
            # Threshold should be background + some margin, but not too high
            adaptive_threshold = mean_energy + (std_energy * self.speech_multiplier)
            self.energy_threshold = max(0.001, min(0.01, adaptive_threshold))

    def process_frame(self, audio_frame: np.ndarray) -> Tuple[bool, Optional[AudioSegment]]:
        """
        Process audio frame with enhanced timing and word boundary detection.

        Args:
            audio_frame: Audio frame to process

        Returns:
            Tuple of (is_voice_detected, audio_segment_if_ready)
        """
        # Process the frame in smaller chunks if it's larger than expected
        segments_ready = []
        is_voice_detected = False

        # Split large frames into smaller VAD-sized chunks
        start_idx = 0
        while start_idx < len(audio_frame):
            end_idx = min(start_idx + self.frame_size, len(audio_frame))
            chunk = audio_frame[start_idx:end_idx]

            # Pad chunk to expected size if needed
            if len(chunk) < self.frame_size:
                chunk = np.pad(chunk, (0, self.frame_size - len(chunk)))

            voice_detected, segment = self._process_single_frame(chunk)
            if voice_detected:
                is_voice_detected = True
            if segment:
                segments_ready.append(segment)
            start_idx = end_idx

        # Return the most recent segment if any are ready
        return is_voice_detected, segments_ready[-1] if segments_ready else None

    def _process_single_frame(self, audio_frame: np.ndarray) -> Tuple[bool, Optional[AudioSegment]]:
        """Process a single frame with enhanced timing and word boundary detection."""
        current_time = datetime.now()

        # Calculate energy
        energy = self.calculate_energy(audio_frame)

        # Update adaptive threshold
        if self.adaptive_threshold:
            self.update_adaptive_threshold(energy)

        # Debug output every N frames (if enabled)
        self.frame_count += 1
        if self.debug_enabled and self.frame_count % self.debug_interval == 0:
            print(f"🔍 Debug: Frame {self.frame_count}, Energy: {energy:.6f}, Threshold: {self.energy_threshold:.6f}, Speaking: {self.is_speaking}")

        # Voice activity detection
        is_voice = energy > self.energy_threshold

        # Always add to pre-speech buffer for padding
        self.pre_speech_buffer.append(audio_frame)

        if is_voice:
            if not self.is_speaking:
                # Start of new speech segment
                self.is_speaking = True
                self.speech_start_time = current_time
                self.segment_start_time = current_time
                self.segment_frames = list(self.pre_speech_buffer)  # Include padding
                print(f"🎤 Voice detected - recording... (threshold: {self.energy_threshold:.4f}, energy: {energy:.4f})")
            else:
                # Continue recording speech
                self.segment_frames.append(audio_frame)

            self.consecutive_silence = 0

            # Check if we should create a segment based on time (1 second rule)
            if self.segment_start_time:
                elapsed_ms = (current_time - self.segment_start_time).total_seconds() * 1000
                if elapsed_ms >= self.max_segment_duration_ms and len(self.segment_frames) >= self.min_speech_frames:
                    return self._create_segment(is_complete=False, is_word_boundary=False)
        else:
            if self.is_speaking:
                # Silent frame during speech
                self.segment_frames.append(audio_frame)
                self.consecutive_silence += 1

                # Check for word boundary (shorter silence)
                if self.consecutive_silence >= self.word_boundary_frames:
                    if len(self.segment_frames) >= self.min_speech_frames:
                        return self._create_segment(is_complete=False, is_word_boundary=True)

                # Check for end of speech (longer silence)
                if self.consecutive_silence >= self.silence_frames:
                    if len(self.segment_frames) >= self.min_speech_frames:
                        return self._create_segment(is_complete=True, is_word_boundary=False)
                    else:
                        # Reset without creating segment
                        self._reset_segment_state()

        return is_voice, None

    def _create_segment(self, is_complete: bool, is_word_boundary: bool) -> Tuple[bool, AudioSegment]:
        """Create an AudioSegment from current frames."""
        if not self.segment_frames:
            return False, None

        # Create audio segment
        audio_data = np.concatenate(self.segment_frames)
        segment = AudioSegment(
            audio_data=audio_data,
            timestamp=self.segment_start_time or datetime.now(),
            is_complete=is_complete,
            is_word_boundary=is_word_boundary
        )

        self.segments_created += 1
        duration = segment.duration

        if is_complete:
            print(f"🔇 Speech ended - segment ready ({duration:.2f}s)")
            self._reset_segment_state()
        elif is_word_boundary:
            print(f"📝 Word boundary - segment ready ({duration:.2f}s)")
            self._start_new_segment()
        else:
            print(f"⏰ Time limit - segment ready ({duration:.2f}s)")
            self._start_new_segment()

        return False, segment

    def _reset_segment_state(self):
        """Reset all segment state for new speech detection."""
        self.is_speaking = False
        self.consecutive_silence = 0
        self.segment_frames = []
        self.segment_start_time = None
        self.speech_start_time = None

    def _start_new_segment(self):
        """Start a new segment while continuing speech detection."""
        self.consecutive_silence = 0
        self.segment_frames = []
        self.segment_start_time = datetime.now()


class AudioDeviceManager:
    """Manages audio input devices and selects the best one."""
    
    def __init__(self):
        self.audio = pyaudio.PyAudio()
    
    def get_audio_devices(self) -> List[Dict[str, Any]]:
        """Get all available audio input devices."""
        devices = []
        device_count = self.audio.get_device_count()
        
        for i in range(device_count):
            try:
                device_info = self.audio.get_device_info_by_index(i)
                if device_info['maxInputChannels'] > 0:  # Input device
                    devices.append({
                        'index': i,
                        'name': device_info['name'],
                        'channels': device_info['maxInputChannels'],
                        'sample_rate': int(device_info['defaultSampleRate']),
                        'host_api': self.audio.get_host_api_info_by_index(device_info['hostApi'])['name']
                    })
            except Exception:
                continue
        
        return devices
    
    def select_best_device(self) -> Optional[Dict[str, Any]]:
        """Select the best available audio input device."""
        devices = self.get_audio_devices()
        
        if not devices:
            return None
        
        # Prioritize devices with higher sample rates and more channels
        # Also prefer certain host APIs (DirectSound, WASAPI on Windows)
        def device_score(device):
            score = 0
            score += device['sample_rate'] / 1000  # Higher sample rate is better
            score += device['channels'] * 10  # More channels is better
            
            # Prefer certain host APIs
            preferred_apis = ['WASAPI', 'DirectSound', 'Core Audio', 'ALSA']
            if device['host_api'] in preferred_apis:
                score += 100
            
            # Prefer devices with "microphone" or "mic" in the name
            name_lower = device['name'].lower()
            if 'microphone' in name_lower or 'mic' in name_lower:
                score += 50
            
            return score
        
        best_device = max(devices, key=device_score)
        return best_device
    
    def list_devices(self):
        """Print all available audio input devices."""
        devices = self.get_audio_devices()
        
        print("\n📱 Available Audio Input Devices:")
        print("-" * 60)
        
        for device in devices:
            print(f"  [{device['index']}] {device['name']}")
            print(f"      Channels: {device['channels']}, Sample Rate: {device['sample_rate']} Hz")
            print(f"      Host API: {device['host_api']}")
            print()
    
    def cleanup(self):
        """Clean up PyAudio resources."""
        self.audio.terminate()


class RealTimeSpeechToText:
    """Enhanced real-time speech-to-text with true parallel processing."""

    def __init__(self, model_name: str = 'base', language: Optional[str] = None,
                 output_file: Optional[str] = None, debug_vad: bool = False):
        """
        Initialize the enhanced real-time speech-to-text converter.

        Args:
            model_name: Whisper model to use
            language: Language code (e.g., 'en', 'es', 'fr')
            output_file: Path to output text file (optional)
            debug_vad: Enable VAD debug output
        """
        print(f"🔄 Loading Whisper model '{model_name}'...")
        self.model = load_whisper_model(model_name)
        self.language = language
        print(f"✅ Model '{model_name}' loaded successfully!")

        # Audio settings
        self.sample_rate = 16000  # Whisper expects 16kHz
        self.frame_duration_ms = 30  # VAD frame duration
        self.min_speech_duration = 0.5  # Minimum duration for transcription (increased for quality)

        # Voice Activity Detection
        self.vad = VoiceActivityDetector(self.sample_rate, self.frame_duration_ms)
        self.vad.debug_enabled = debug_vad

        # Enhanced queue system with size limits
        self.raw_audio_queue = queue.Queue(maxsize=100)  # Raw audio frames
        self.audio_segment_queue = queue.Queue(maxsize=50)  # AudioSegment objects
        self.transcription_queue = queue.Queue(maxsize=100)  # TranscriptionResult objects

        # Thread management
        self.is_running = False
        self.threads = {}
        self.device_manager = AudioDeviceManager()

        # Output management
        self.output_file = output_file
        self.temp_transcriptions = []  # Temporary buffer for transcriptions
        self.output_lock = threading.Lock()
        self.final_output_file = "final_transcribed.txt"

        # Statistics and monitoring
        self.segments_processed = 0
        self.transcription_errors = 0
        self.start_time = None
        self.session_id = str(uuid.uuid4())[:8]
        
    def audio_callback(self, in_data, frame_count, time_info, status):
        """Enhanced audio callback with queue overflow protection."""
        if status:
            print(f"⚠️  Audio status: {status}")

        # Convert bytes to numpy array
        audio_data = np.frombuffer(in_data, dtype=np.float32)

        # Non-blocking queue put with overflow protection
        try:
            self.raw_audio_queue.put_nowait(audio_data)
        except queue.Full:
            # Drop oldest frame if queue is full
            try:
                self.raw_audio_queue.get_nowait()
                self.raw_audio_queue.put_nowait(audio_data)
            except queue.Empty:
                pass

        return (in_data, pyaudio.paContinue)
    
    def voice_detection_thread(self):
        """Enhanced voice detection thread with AudioSegment processing."""
        print("🔍 Enhanced voice detection thread started")
        thread_id = threading.current_thread().ident

        while self.is_running:
            try:
                # Get raw audio data with timeout
                audio_chunk = self.raw_audio_queue.get(timeout=0.1)

                # Process with enhanced VAD
                is_voice, audio_segment = self.vad.process_frame(audio_chunk)

                # If we have a ready audio segment, queue it for transcription
                if audio_segment is not None:
                    # Check minimum duration
                    if audio_segment.duration >= self.min_speech_duration:
                        try:
                            self.audio_segment_queue.put_nowait(audio_segment)
                            segment_type = "complete" if audio_segment.is_complete else "word boundary" if audio_segment.is_word_boundary else "time limit"
                            print(f"📝 Audio segment queued ({audio_segment.duration:.2f}s, {segment_type}, ID: {audio_segment.id})")
                        except queue.Full:
                            print("⚠️  Audio segment queue full, dropping oldest segment")
                            try:
                                self.audio_segment_queue.get_nowait()
                                self.audio_segment_queue.put_nowait(audio_segment)
                            except queue.Empty:
                                pass
                    else:
                        print(f"⏭️  Audio segment too short ({audio_segment.duration:.2f}s < {self.min_speech_duration}s), skipping")

            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ Error in voice detection thread (ID: {thread_id}): {e}")
                if not self.is_running:
                    break

    def transcription_thread(self):
        """Enhanced transcription thread with proper error handling."""
        print("🎯 Enhanced transcription thread started")
        thread_id = threading.current_thread().ident

        while self.is_running:
            try:
                # Get audio segment to transcribe
                audio_segment = self.audio_segment_queue.get(timeout=0.1)

                # Transcribe the audio segment
                start_time = time.time()
                text = self.transcribe_audio_segment(audio_segment)
                processing_time = time.time() - start_time

                if text:
                    # Create transcription result
                    result = TranscriptionResult(
                        id=audio_segment.id,
                        text=text,
                        timestamp=audio_segment.timestamp,
                        duration=audio_segment.duration,
                        processing_time=processing_time
                    )

                    # Queue transcription result for output
                    try:
                        self.transcription_queue.put_nowait(result)
                        self.segments_processed += 1
                        print(f"✅ Transcribed segment {audio_segment.id} in {processing_time:.2f}s")
                    except queue.Full:
                        print("⚠️  Transcription queue full, dropping oldest result")
                        try:
                            self.transcription_queue.get_nowait()
                            self.transcription_queue.put_nowait(result)
                        except queue.Empty:
                            pass
                else:
                    print(f"⚠️  Empty transcription for segment {audio_segment.id}")

            except queue.Empty:
                continue
            except Exception as e:
                self.transcription_errors += 1
                print(f"❌ Error in transcription thread (ID: {thread_id}): {e}")
                if not self.is_running:
                    break

    def transcribe_audio_segment(self, audio_segment: AudioSegment) -> str:
        """Transcribe an AudioSegment using Whisper."""
        try:
            # Ensure minimum length for Whisper
            min_samples = int(0.1 * self.sample_rate)  # 0.1 second minimum
            if len(audio_segment.audio_data) < min_samples:
                return ""

            # Transcribe the audio segment
            result = self.model.transcribe(
                audio_segment.audio_data,
                language=self.language,
                verbose=False,
                fp16=False  # Use fp32 for better compatibility
            )

            text = result['text'].strip()
            return text if text else ""

        except Exception as e:
            print(f"❌ Error transcribing audio segment {audio_segment.id}: {e}")
            return ""

    def output_thread(self):
        """Enhanced output thread with temporary buffer management."""
        print("📄 Enhanced output thread started")
        thread_id = threading.current_thread().ident

        while self.is_running:
            try:
                # Get transcription result
                result = self.transcription_queue.get(timeout=0.1)

                # Format timestamp for display
                display_time = result.timestamp.strftime("%H:%M:%S")

                # Display to console immediately
                print(f"🎤 [{display_time}] {result.text}")

                # Add to temporary buffer
                with self.output_lock:
                    self.temp_transcriptions.append(result)

                # Write to temporary file if specified
                if self.output_file:
                    self.write_to_temp_file(result)

            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ Error in output thread (ID: {thread_id}): {e}")
                if not self.is_running:
                    break

    def write_to_temp_file(self, result: TranscriptionResult):
        """Write transcription result to temporary file."""
        try:
            timestamp_str = result.timestamp.strftime("%H:%M:%S")
            with open(self.output_file, 'a', encoding='utf-8') as f:
                f.write(f"[{timestamp_str}] {result.text}\n")
                f.flush()  # Ensure immediate write
        except Exception as e:
            print(f"❌ Error writing to temp file: {e}")

    def consolidate_final_output(self):
        """Consolidate all transcriptions into final output file."""
        try:
            with self.output_lock:
                if not self.temp_transcriptions:
                    print("📄 No transcriptions to consolidate")
                    return

                # Sort transcriptions by timestamp
                sorted_transcriptions = sorted(self.temp_transcriptions, key=lambda x: x.timestamp)

                # Write to final file
                with open(self.final_output_file, 'w', encoding='utf-8') as f:
                    f.write(f"# Speech-to-Text Session {self.session_id}\n")
                    f.write(f"# Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"# Total segments: {len(sorted_transcriptions)}\n")
                    f.write(f"# Session duration: {time.time() - self.start_time:.1f} seconds\n\n")

                    for result in sorted_transcriptions:
                        timestamp_str = result.timestamp.strftime("%H:%M:%S")
                        f.write(f"[{timestamp_str}] {result.text}\n")

                print(f"📄 Final transcription saved to: {self.final_output_file}")
                print(f"   Total segments: {len(sorted_transcriptions)}")

        except Exception as e:
            print(f"❌ Error consolidating final output: {e}")

    def print_statistics(self):
        """Print enhanced processing statistics."""
        if self.start_time:
            elapsed = time.time() - self.start_time
            print(f"\n📊 Enhanced Session Statistics (ID: {self.session_id}):")
            print(f"   Duration: {elapsed:.1f} seconds")
            print(f"   Segments processed: {self.segments_processed}")
            print(f"   Transcription errors: {self.transcription_errors}")
            print(f"   VAD segments created: {self.vad.segments_created}")

            # Queue status
            print(f"   Queue status:")
            print(f"     Raw audio queue: {self.raw_audio_queue.qsize()}")
            print(f"     Audio segment queue: {self.audio_segment_queue.qsize()}")
            print(f"     Transcription queue: {self.transcription_queue.qsize()}")

            if self.segments_processed > 0:
                print(f"   Average processing rate: {self.segments_processed/elapsed:.1f} segments/second")
                success_rate = (self.segments_processed / (self.segments_processed + self.transcription_errors)) * 100
                print(f"   Success rate: {success_rate:.1f}%")

            if self.output_file:
                print(f"   Temporary output: {self.output_file}")
            print(f"   Final output: {self.final_output_file}")
    
    def start_listening(self):
        """Start enhanced real-time audio capture with true parallel processing."""
        # Select the best audio device
        device = self.device_manager.select_best_device()

        if not device:
            print("❌ No audio input devices found!")
            return False

        print(f"🎯 Selected audio device: {device['name']}")
        print(f"   Sample Rate: {device['sample_rate']} Hz, Channels: {device['channels']}")
        print(f"📄 Session ID: {self.session_id}")
        if self.output_file:
            print(f"📄 Temporary output: {self.output_file}")
        print(f"📄 Final output: {self.final_output_file}")
        print()

        try:
            # Start audio stream
            stream = self.device_manager.audio.open(
                format=pyaudio.paFloat32,
                channels=1,  # Mono
                rate=self.sample_rate,
                input=True,
                input_device_index=device['index'],
                frames_per_buffer=1024,
                stream_callback=self.audio_callback
            )

            # Start all processing threads with enhanced management
            self.is_running = True
            self.start_time = time.time()

            # Voice detection thread (Core 1: Real-time audio processing)
            self.threads['vad'] = threading.Thread(target=self.voice_detection_thread, name="VAD-Thread")
            self.threads['vad'].daemon = True
            self.threads['vad'].start()

            # Transcription thread (Core 2: Parallel transcription processing)
            self.threads['transcription'] = threading.Thread(target=self.transcription_thread, name="Transcription-Thread")
            self.threads['transcription'].daemon = True
            self.threads['transcription'].start()

            # Output thread (Core 3: Output management)
            self.threads['output'] = threading.Thread(target=self.output_thread, name="Output-Thread")
            self.threads['output'].daemon = True
            self.threads['output'].start()

            # Start the audio stream
            stream.start_stream()

            print("🎙️  Enhanced Real-time Speech Recognition Started!")
            print("🔄 True parallel processing with 3 dedicated threads")
            print("⏱️  Processing: 1-second intervals OR word boundaries")
            print("💬 Start speaking... (Press Ctrl+C to stop)")
            print("=" * 70)

            # Keep the main thread alive and monitor threads
            try:
                while stream.is_active() and self.is_running:
                    time.sleep(0.5)

                    # Check if all threads are still alive
                    dead_threads = [name for name, thread in self.threads.items() if not thread.is_alive()]
                    if dead_threads:
                        print(f"⚠️  Warning: Threads died: {dead_threads}")

            except KeyboardInterrupt:
                print("\n\n🛑 Stopping enhanced speech recognition...")

            # Graceful shutdown
            self.is_running = False

            # Wait for threads to finish processing remaining items
            print("⏳ Waiting for threads to finish...")
            for name, thread in self.threads.items():
                thread.join(timeout=2.0)
                if thread.is_alive():
                    print(f"⚠️  Thread {name} did not finish gracefully")

            # Stop audio stream
            stream.stop_stream()
            stream.close()

            # Consolidate final output
            print("📄 Consolidating final transcription...")
            self.consolidate_final_output()

            # Print final statistics
            self.print_statistics()

            return True

        except Exception as e:
            print(f"❌ Error starting enhanced audio stream: {e}")
            return False
    
    def cleanup(self):
        """Enhanced cleanup with queue clearing and final consolidation."""
        print("🧹 Cleaning up resources...")
        self.is_running = False

        # Clear all queues
        self._clear_queue(self.raw_audio_queue, "raw audio")
        self._clear_queue(self.audio_segment_queue, "audio segment")
        self._clear_queue(self.transcription_queue, "transcription")

        # Final consolidation if not already done
        if self.temp_transcriptions:
            print("📄 Final consolidation during cleanup...")
            self.consolidate_final_output()

        # Cleanup device manager
        self.device_manager.cleanup()
        print("✅ Cleanup completed")

    def _clear_queue(self, q: queue.Queue, name: str):
        """Clear a queue and report the number of items dropped."""
        count = 0
        try:
            while True:
                q.get_nowait()
                count += 1
        except queue.Empty:
            pass
        if count > 0:
            print(f"   Cleared {count} items from {name} queue")


def main():
    """Main function for the real-time speech-to-text CLI."""
    parser = argparse.ArgumentParser(
        description="Real-time Speech-to-Text using OpenAI Whisper",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python realtime_speech_to_text.py
  python realtime_speech_to_text.py --model small --language en
  python realtime_speech_to_text.py --output temp_transcription.txt
  python realtime_speech_to_text.py --model base --language en --output temp.txt
  python realtime_speech_to_text.py --debug-vad --output debug_session.txt
  python realtime_speech_to_text.py --list-devices

Features:
  - True parallel processing with 3 dedicated threads
  - 1-second intervals OR word boundary detection
  - Real-time console output with timestamps
  - Temporary file output during session
  - Final consolidated output in 'final_transcribed.txt'
  - Enhanced error handling and graceful degradation
        """
    )
    
    parser.add_argument(
        '--model', '-m',
        type=str,
        default='base',
        choices=['tiny', 'base', 'small', 'medium', 'large'],
        help='Whisper model to use (default: base)'
    )
    
    parser.add_argument(
        '--language', '-l',
        type=str,
        help='Language code (e.g., en, es, fr). Auto-detect if not specified'
    )
    
    parser.add_argument(
        '--list-devices',
        action='store_true',
        help='List available audio input devices and exit'
    )

    parser.add_argument(
        '--output', '-o',
        type=str,
        help='Output text file to save transcriptions (optional)'
    )

    parser.add_argument(
        '--debug-vad',
        action='store_true',
        help='Enable Voice Activity Detection debug output'
    )

    args = parser.parse_args()
    
    # Initialize device manager for listing devices
    if args.list_devices:
        device_manager = AudioDeviceManager()
        device_manager.list_devices()
        device_manager.cleanup()
        return
    
    print("🎯 Enhanced Real-time Speech-to-Text CLI")
    print("🔄 True Parallel Processing | 1s Intervals | Word Boundaries")
    print("=" * 60)
    
    try:
        # Initialize the speech-to-text converter
        converter = RealTimeSpeechToText(
            model_name=args.model,
            language=args.language,
            output_file=args.output,
            debug_vad=args.debug_vad
        )
        
        # Start listening
        success = converter.start_listening()
        
        # Cleanup
        converter.cleanup()
        
        if success:
            print("✅ Session completed successfully!")
        else:
            print("❌ Session failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n🛑 Application interrupted by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
