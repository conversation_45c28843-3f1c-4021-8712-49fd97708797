# Speech-to-Text Converter

A Python script that converts speech from audio files to text using OpenAI's Whisper model - the most popular and accurate open-source speech recognition solution.

## Features

- **High Accuracy**: Uses OpenAI Whisper, state-of-the-art speech recognition
- **Multiple Formats**: Supports MP3, WAV, M4A, FLAC, OGG, WMA, AAC
- **Batch Processing**: Processes entire directories of audio files
- **Multiple Models**: Choose from tiny, base, small, medium, or large models
- **Language Support**: Auto-detection or manual language specification
- **Timestamps**: Optional timestamp inclusion in transcriptions
- **Error Handling**: Robust error handling and progress tracking

## Installation

1. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Note**: First run will download the selected Whisper model (can be 100MB-3GB depending on model size)

## Usage

### Basic Usage
```bash
python speech_to_text.py ./audio_files
```

### Advanced Usage
```bash
# Use a larger model for better accuracy
python speech_to_text.py ./audio_files --model medium

# Specify output directory
python speech_to_text.py ./audio_files --output ./my_transcripts

# Include timestamps
python speech_to_text.py ./audio_files --timestamps

# Specify language (skips auto-detection)
python speech_to_text.py ./audio_files --language en

# Combine options
python speech_to_text.py ./audio_files --model large --output ./transcripts --language en --timestamps
```

## Model Options

| Model  | Size | Speed | Accuracy | Use Case |
|--------|------|-------|----------|----------|
| tiny   | 39MB | Fastest | Good | Quick testing, real-time |
| base   | 74MB | Fast | Better | General use (default) |
| small  | 244MB | Medium | Good | Balanced speed/accuracy |
| medium | 769MB | Slow | Very Good | High accuracy needed |
| large  | 1550MB | Slowest | Best | Maximum accuracy |

## Supported Audio Formats

- MP3
- WAV
- M4A
- FLAC
- OGG
- WMA
- AAC

## Output

The script creates text files with the following naming convention:
- Input: `meeting.mp3`
- Output: `meeting_transcript.txt`

Each transcript includes:
- Original filename
- Model used
- Detected language
- Transcribed text (with optional timestamps)

## Examples

### Example 1: Basic transcription
```bash
python speech_to_text.py ./recordings
```

### Example 2: High-accuracy transcription with timestamps
```bash
python speech_to_text.py ./interviews --model large --timestamps --output ./interview_transcripts
```

### Example 3: Spanish audio files
```bash
python speech_to_text.py ./spanish_audio --language es --model medium
```

## Performance Tips

1. **Model Selection**: Start with 'base' model, upgrade to 'medium' or 'large' if accuracy is insufficient
2. **Language Specification**: Specifying language improves speed and accuracy
3. **File Formats**: WAV and FLAC generally provide best results
4. **Hardware**: GPU acceleration automatically used if available (CUDA/MPS)

## Troubleshooting

### Common Issues

1. **"No audio files found"**
   - Check that your directory contains supported audio formats
   - Ensure file extensions are correct (.mp3, .wav, etc.)

2. **"Model download failed"**
   - Check internet connection
   - Ensure sufficient disk space (models can be large)

3. **"Out of memory"**
   - Try a smaller model (tiny or base)
   - Process files individually instead of batch

4. **Poor transcription quality**
   - Try a larger model (medium or large)
   - Ensure audio quality is good (clear speech, minimal background noise)
   - Specify the correct language if auto-detection fails

## Requirements

- Python 3.7+
- Internet connection (for initial model download)
- Sufficient disk space for models
- Optional: CUDA-compatible GPU for faster processing

## License

This script uses OpenAI Whisper, which is released under MIT License.
