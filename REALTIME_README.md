# Real-time Speech-to-Text CLI

A Python CLI application that provides real-time speech-to-text transcription using OpenAI Whisper.

## Features

- 🎯 **Automatic Audio Device Selection**: Automatically detects and selects the best available microphone
- 🎙️ **Real-time Processing**: Transcribes speech as you talk with minimal delay
- 🌍 **Multi-language Support**: Supports multiple languages with auto-detection
- 📱 **Device Management**: Lists and manages audio input devices
- ⚡ **Multiple Model Options**: Choose from different Whisper models based on accuracy vs speed needs

## Installation

1. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Install PyAudio** (if you encounter issues):
   
   **Windows**:
   ```bash
   pip install pyaudio
   ```
   
   **macOS**:
   ```bash
   brew install portaudio
   pip install pyaudio
   ```
   
   **Linux (Ubuntu/Debian)**:
   ```bash
   sudo apt-get install portaudio19-dev python3-pyaudio
   pip install pyaudio
   ```

## Usage

### Basic Usage

Start real-time speech recognition with default settings:
```bash
python realtime_speech_to_text.py
```

### Advanced Options

**Use a specific Whisper model**:
```bash
python realtime_speech_to_text.py --model small
```

**Specify language** (for better accuracy):
```bash
python realtime_speech_to_text.py --language en
```

**List available audio devices**:
```bash
python realtime_speech_to_text.py --list-devices
```

**Combine options**:
```bash
python realtime_speech_to_text.py --model medium --language en
```

## Available Models

| Model  | Size | Speed | Accuracy | Use Case |
|--------|------|-------|----------|----------|
| tiny   | 39 MB | Fastest | Basic | Quick testing |
| base   | 74 MB | Fast | Good | Default choice |
| small  | 244 MB | Medium | Better | Balanced |
| medium | 769 MB | Slow | High | High accuracy |
| large  | 1550 MB | Slowest | Highest | Best quality |

## Supported Languages

The application supports all languages that Whisper supports, including:
- English (en)
- Spanish (es)
- French (fr)
- German (de)
- Italian (it)
- Portuguese (pt)
- Russian (ru)
- Japanese (ja)
- Korean (ko)
- Chinese (zh)
- And many more...

## How It Works

1. **Device Detection**: Automatically scans and ranks available audio input devices
2. **Audio Capture**: Captures audio in real-time using PyAudio
3. **Chunking**: Processes audio in 3-second overlapping chunks
4. **Transcription**: Uses Whisper to convert speech to text
5. **Display**: Shows transcribed text in real-time in the CLI

## Troubleshooting

### No Audio Devices Found
- Check that your microphone is connected and working
- Run with `--list-devices` to see available devices
- Try running as administrator (Windows) or with sudo (Linux)

### Poor Audio Quality
- Check microphone settings in your system
- Try a different Whisper model (larger models are more accurate)
- Ensure you're speaking clearly and close to the microphone

### Performance Issues
- Use a smaller Whisper model (tiny or base)
- Close other applications that might be using the microphone
- Ensure your system meets the requirements

### PyAudio Installation Issues
- On Windows: Try installing Visual C++ Build Tools
- On macOS: Install Xcode command line tools
- On Linux: Install development packages for PortAudio

## Controls

- **Start**: Run the application and start speaking
- **Stop**: Press `Ctrl+C` to stop the application
- **View Devices**: Use `--list-devices` flag

## Tips for Best Results

1. **Speak clearly** and at a normal pace
2. **Minimize background noise** for better accuracy
3. **Use a good quality microphone** if possible
4. **Choose the right model** - larger models are more accurate but slower
5. **Specify language** if you know it for better performance

## Examples

**Quick start with English**:
```bash
python realtime_speech_to_text.py --language en
```

**High accuracy mode**:
```bash
python realtime_speech_to_text.py --model large --language en
```

**Fast mode for testing**:
```bash
python realtime_speech_to_text.py --model tiny
```

## Building Standalone Binary

You can create a standalone executable that doesn't require Python to be installed on the target machine.

### Using PyInstaller

1. **Install PyInstaller**:
   ```bash
   pip install pyinstaller
   ```

2. **Create basic executable**:
   ```bash
   pyinstaller --onefile realtime_speech_to_text.py
   ```

3. **Create optimized executable with icon** (recommended):
   ```bash
   pyinstaller --onefile --noconsole --name "RealTimeSpeechToText" realtime_speech_to_text.py
   ```

4. **Advanced build with hidden imports** (if you encounter import errors):
   ```bash
   pyinstaller --onefile --hidden-import=whisper --hidden-import=pyaudio --hidden-import=numpy --name "RealTimeSpeechToText" realtime_speech_to_text.py
   ```

### Build Output

- The executable will be created in the `dist/` folder
- File size will be approximately 100-200 MB (includes Python runtime)
- The executable is portable and can run on machines without Python

### Cross-Platform Building

- **Windows**: Creates `.exe` file
- **macOS**: Creates Unix executable
- **Linux**: Creates Unix executable

Note: You must build on the target platform (Windows builds require Windows, etc.)

## Whisper Models Storage & Distribution

### Model Storage Locations

Whisper models are automatically downloaded and cached in:

**Windows**:
```
C:\Users\<USER>\.cache\whisper\
```

**macOS**:
```
~/.cache/whisper/
```

**Linux**:
```
~/.cache/whisper/
```

### Model Files

| Model | File Size | Filename |
|-------|-----------|----------|
| tiny | ~39 MB | `tiny.pt` |
| base | ~74 MB | `base.pt` |
| small | ~244 MB | `small.pt` |
| medium | ~769 MB | `medium.pt` |
| large | ~1550 MB | `large-v3.pt` |

### Including Models in Binary Distribution

#### Option 1: Pre-download Models (Recommended)

1. **Download models beforehand**:
   ```bash
   python -c "import whisper; whisper.load_model('base')"
   python -c "import whisper; whisper.load_model('small')"
   ```

2. **Create distribution package**:
   ```bash
   # Create a models directory
   mkdir models

   # Copy models from cache
   # Windows:
   copy "%USERPROFILE%\.cache\whisper\*.pt" models\

   # macOS/Linux:
   cp ~/.cache/whisper/*.pt models/
   ```

3. **Modify the application** to look for local models first (see code modification below)

#### Option 2: Bundle Models with PyInstaller

1. **Create a spec file for PyInstaller**:
   ```bash
   pyinstaller --onefile --name "RealTimeSpeechToText" realtime_speech_to_text.py --specpath .
   ```

2. **Edit the generated `.spec` file** to include models:
   ```python
   # Add to the Analysis section:
   datas=[('models/*.pt', 'whisper_models')]
   ```

3. **Rebuild with the spec file**:
   ```bash
   pyinstaller RealTimeSpeechToText.spec
   ```

#### Automated Build Script (Recommended)

Use the provided build script for easy executable creation:

**Windows**:
```bash
# Simple build with basic models
build.bat

# Or use the Python script directly
python build_binary.py --models tiny base small
```

**macOS/Linux**:
```bash
# Make script executable and run
chmod +x build.sh
./build.sh

# Or use the Python script directly
python build_binary.py --models tiny base small
```

#### Build Script Options

```bash
# Build with specific models
python build_binary.py --models base medium

# Build without bundled models (smaller executable)
python build_binary.py --no-models

# Build with all models (large file size)
python build_binary.py --models tiny base small medium large

# Skip dependency installation
python build_binary.py --skip-deps
```

#### Manual Build Process

If you prefer manual control:

1. **Pre-download models**:
   ```python
   import whisper
   whisper.load_model('base')  # Downloads and caches the model
   ```

2. **Create models directory**:
   ```bash
   mkdir models
   # Copy from cache (Windows)
   copy "%USERPROFILE%\.cache\whisper\*.pt" models\
   # Copy from cache (macOS/Linux)
   cp ~/.cache/whisper/*.pt models/
   ```

3. **Build executable**:
   ```bash
   pyinstaller --onefile --name "RealTimeSpeechToText" realtime_speech_to_text.py
   ```

### Distribution Package Structure

After building, you'll get a `distribution` folder with:

```
distribution/
├── RealTimeSpeechToText.exe    # Main executable
├── models/                     # Whisper models (if included)
│   ├── tiny.pt
│   ├── base.pt
│   └── small.pt
├── REALTIME_README.md          # Documentation
├── README.md                   # Original documentation
└── start.bat                   # Windows launcher script
```

### Deployment

1. **Zip the distribution folder** for easy sharing
2. **Users only need to**:
   - Extract the zip file
   - Run `RealTimeSpeechToText.exe` (Windows) or `./RealTimeSpeechToText` (Unix)
   - No Python installation required!

### Build Size Estimates

| Configuration | Approximate Size |
|---------------|------------------|
| No models bundled | ~100-150 MB |
| With tiny + base | ~200-250 MB |
| With tiny + base + small | ~400-500 MB |
| With all models | ~2-3 GB |

**Recommendation**: Bundle `tiny`, `base`, and `small` models for a good balance of size and functionality.

## Model Management

### Where Models Are Stored

The application looks for models in this order:

1. **Local `models/` directory** (bundled with executable)
2. **`whisper_models/` directory** (alternative location)
3. **Next to executable** (for portable installations)
4. **User cache directory** (standard Whisper location)
5. **Downloads from internet** (fallback)

### Model Download Behavior

- **First run**: Downloads models to cache if not found locally
- **Subsequent runs**: Uses cached models
- **Bundled executable**: Uses included models, no download needed
- **Offline usage**: Works if models are bundled or pre-downloaded

### Managing Model Storage

**Check current model cache**:
```bash
# Windows
dir "%USERPROFILE%\.cache\whisper"

# macOS/Linux
ls ~/.cache/whisper/
```

**Clear model cache** (to save space):
```bash
# Windows
rmdir /s "%USERPROFILE%\.cache\whisper"

# macOS/Linux
rm -rf ~/.cache/whisper/
```

**Pre-download specific models**:
```python
import whisper

# Download models you want
models = ['tiny', 'base', 'small']
for model in models:
    print(f"Downloading {model}...")
    whisper.load_model(model)
    print(f"✅ {model} downloaded")
```
